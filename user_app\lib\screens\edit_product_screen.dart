import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import '../constants/app_constants.dart';
import '../models/product_model.dart';
import '../services/product_service.dart';
import '../services/image_service.dart';
import '../widgets/product_form/product_form_widgets.dart';

class EditProductScreen extends StatefulWidget {
  final ProductModel product;

  const EditProductScreen({
    super.key,
    required this.product,
  });

  @override
  State<EditProductScreen> createState() => _EditProductScreenState();
}

class _EditProductScreenState extends State<EditProductScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _originalPriceController = TextEditingController();
  final _stockController = TextEditingController();
  final _liveLinkController = TextEditingController();

  List<String> _existingImages = [];
  List<XFile> _newImages = [];
  List<String> _tags = [];
  String _selectedCategory = '';
  bool _isAvailable = true;
  bool _isLoading = false;

  final List<String> _categories = [
    'Electronics',
    'Fashion',
    'Home & Garden',
    'Sports',
    'Books',
    'Toys',
    'Health & Beauty',
    'Automotive',
    'Food & Beverages',
    'Others',
  ];

  @override
  void initState() {
    super.initState();
    _initializeFields();
  }

  void _initializeFields() {
    _nameController.text = widget.product.name;
    _descriptionController.text = widget.product.description;
    _priceController.text = widget.product.price > 0 ? widget.product.price.toString() : '';
    _originalPriceController.text = widget.product.originalPrice?.toString() ?? '';
    _stockController.text = widget.product.stockQuantity.toString();
    _liveLinkController.text = widget.product.liveLink ?? '';
    
    _existingImages = List.from(widget.product.imageUrls);
    _tags = List.from(widget.product.tags);
    _selectedCategory = widget.product.category;
    _isAvailable = widget.product.isAvailable;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _originalPriceController.dispose();
    _stockController.dispose();
    _liveLinkController.dispose();
    super.dispose();
  }

  Future<void> _pickImages() async {
    try {
      final totalImages = _existingImages.length + _newImages.length;
      final remainingSlots = 10 - totalImages;
      
      if (remainingSlots <= 0) {
        _showErrorSnackBar('Maximum 10 images allowed');
        return;
      }

      final images = await ImageService.pickMultipleImages(
        maxImages: remainingSlots,
      );
      
      if (images != null && images.isNotEmpty) {
        setState(() {
          _newImages.addAll(images);
        });
      }
    } catch (e) {
      _showErrorSnackBar('Error picking images: $e');
    }
  }

  void _removeNewImage(int index) {
    setState(() {
      _newImages.removeAt(index);
    });
  }

  void _removeExistingImage(int index) {
    setState(() {
      _existingImages.removeAt(index);
    });
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppConstants.errorColor,
        ),
      );
    }
  }

  Future<void> _updateProduct() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final totalImages = _existingImages.length + _newImages.length;
    if (totalImages == 0) {
      _showErrorSnackBar('Please select at least one image');
      return;
    }

    if (_selectedCategory.isEmpty) {
      _showErrorSnackBar('Please select a category');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Upload new images if any
      List<String> newImageUrls = [];
      if (_newImages.isNotEmpty) {
        newImageUrls = await ImageService.uploadProductImages(
          imageFiles: _newImages,
          productId: widget.product.id,
        );
      }

      // Combine existing and new image URLs
      final allImageUrls = [..._existingImages, ...newImageUrls];

      // Create updated product model
      final updatedProduct = ProductModel(
        id: widget.product.id,
        sellerId: widget.product.sellerId,
        sellerName: widget.product.sellerName,
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        price: _priceController.text.trim().isNotEmpty
            ? double.parse(_priceController.text)
            : widget.product.price,
        originalPrice: _originalPriceController.text.isNotEmpty
            ? double.parse(_originalPriceController.text)
            : null,
        category: _selectedCategory,
        imageUrls: allImageUrls,
        tags: _tags,
        stockQuantity: _stockController.text.trim().isNotEmpty
            ? int.parse(_stockController.text)
            : 999999,
        isAvailable: _isAvailable,
        isFeatured: widget.product.isFeatured, // Keep original featured status
        createdAt: widget.product.createdAt,
        updatedAt: DateTime.now(),
        rating: widget.product.rating,
        reviewCount: widget.product.reviewCount,
        specifications: widget.product.specifications,
        sellerWhatsApp: widget.product.sellerWhatsApp,
        sellerWeChat: widget.product.sellerWeChat,
        sellerLocation: widget.product.sellerLocation,
        liveLink: _liveLinkController.text.trim().isNotEmpty
            ? _liveLinkController.text.trim()
            : null,
        isApproved: widget.product.isApproved,
        isDeleted: widget.product.isDeleted,
        moderatorNote: widget.product.moderatorNote,
        deletedAt: widget.product.deletedAt,
      );

      // Update in Firestore
      final success = await ProductService.updateProduct(updatedProduct);

      if (success) {
        if (mounted) {
          Navigator.of(context).pop(updatedProduct);
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Product updated successfully!'),
              backgroundColor: AppConstants.successColor,
            ),
          );
        }
      } else {
        throw Exception('Failed to update product');
      }
    } catch (e) {
      _showErrorSnackBar('Error updating product: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: AppConstants.fontSizeLarge,
        fontWeight: FontWeight.w600,
        color: AppConstants.textPrimaryColor,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        title: const Text(
          'Edit Product',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: AppConstants.textPrimaryColor,
          ),
        ),
        backgroundColor: AppConstants.surfaceColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppConstants.textPrimaryColor),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          if (_isLoading)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: AppConstants.primaryColor,
                  ),
                ),
              ),
            )
          else
            TextButton(
              onPressed: _updateProduct,
              child: const Text(
                'Update',
                style: TextStyle(
                  color: AppConstants.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.paddingLarge),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Product Images Section
              EditProductImagePicker(
                existingImages: _existingImages,
                newImages: _newImages,
                onPickImages: _pickImages,
                onRemoveNewImage: _removeNewImage,
                onRemoveExistingImage: _removeExistingImage,
              ),

              const SizedBox(height: AppConstants.paddingLarge),

              // Basic Information
              _buildSectionTitle('Basic Information'),
              const SizedBox(height: AppConstants.paddingMedium),

              ProductFormField(
                controller: _nameController,
                label: 'Product Name',
                hint: 'Enter product name',
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Product name is required';
                  }
                  return null;
                },
              ),

              const SizedBox(height: AppConstants.paddingMedium),

              ProductFormField(
                controller: _descriptionController,
                label: 'Description',
                hint: 'Enter product description',
                maxLines: 4,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Description is required';
                  }
                  return null;
                },
              ),

              const SizedBox(height: AppConstants.paddingMedium),

              ProductCategoryDropdown(
                selectedCategory: _selectedCategory,
                categories: _categories,
                onChanged: (value) {
                  setState(() {
                    _selectedCategory = value ?? '';
                  });
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please select a category';
                  }
                  return null;
                },
              ),

              const SizedBox(height: AppConstants.paddingLarge),

              // Pricing Information
              _buildSectionTitle('Pricing Information'),
              const SizedBox(height: AppConstants.paddingMedium),

              Row(
                children: [
                  Expanded(
                    child: ProductFormField(
                      controller: _priceController,
                      label: 'Price (৳)',
                      hint: 'Enter price (optional)',
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                      ],
                      validator: (value) {
                        if (value != null && value.trim().isNotEmpty) {
                          final price = double.tryParse(value);
                          if (price == null || price <= 0) {
                            return 'Enter valid price';
                          }
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: AppConstants.paddingMedium),
                  Expanded(
                    child: ProductFormField(
                      controller: _originalPriceController,
                      label: 'Original Price (৳)',
                      hint: 'Enter original price (optional)',
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                      ],
                      validator: (value) {
                        if (value != null && value.trim().isNotEmpty) {
                          final originalPrice = double.tryParse(value);
                          final price = double.tryParse(_priceController.text);
                          if (originalPrice == null || originalPrice <= 0) {
                            return 'Enter valid original price';
                          }
                          if (price != null && originalPrice <= price) {
                            return 'Original price must be higher than price';
                          }
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),

              const SizedBox(height: AppConstants.paddingMedium),

              ProductFormField(
                controller: _stockController,
                label: 'Stock Quantity',
                hint: 'Enter stock quantity',
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                ],
                validator: (value) {
                  if (value != null && value.trim().isNotEmpty) {
                    final stock = int.tryParse(value);
                    if (stock == null || stock < 0) {
                      return 'Enter valid stock quantity';
                    }
                  }
                  return null;
                },
              ),

              const SizedBox(height: AppConstants.paddingLarge),

              // Live Link
              ProductFormField(
                controller: _liveLinkController,
                label: 'Live Link',
                hint: 'Enter live streaming or video link (optional)',
                keyboardType: TextInputType.url,
                validator: (value) {
                  if (value != null && value.trim().isNotEmpty) {
                    final urlPattern = RegExp(
                      r'^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$'
                    );
                    if (!urlPattern.hasMatch(value.trim())) {
                      return 'Please enter a valid URL';
                    }
                  }
                  return null;
                },
              ),

              const SizedBox(height: AppConstants.paddingLarge),

              // Tags
              ProductTagsField(
                tags: _tags,
                onTagsChanged: (tags) {
                  setState(() {
                    _tags = tags;
                  });
                },
              ),

              const SizedBox(height: AppConstants.paddingLarge),

              // Settings
              _buildSectionTitle('Settings'),
              const SizedBox(height: AppConstants.paddingMedium),

              ProductSwitchField(
                label: 'Available',
                subtitle: 'Make this product available for purchase',
                value: _isAvailable,
                onChanged: (value) {
                  setState(() {
                    _isAvailable = value;
                  });
                },
              ),



              const SizedBox(height: AppConstants.paddingXLarge),
            ],
          ),
        ),
      ),
    );
  }
}
