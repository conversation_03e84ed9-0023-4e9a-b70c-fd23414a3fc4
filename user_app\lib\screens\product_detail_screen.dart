import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:share_plus/share_plus.dart';
import '../constants/app_constants.dart';
import '../models/product_model.dart';
import '../widgets/role_indicator.dart';
import '../services/user_service.dart';
import '../services/settings_service.dart';
import '../models/user_model.dart';

class ProductDetailScreen extends StatefulWidget {
  final ProductModel product;

  const ProductDetailScreen({
    super.key,
    required this.product,
  });

  @override
  State<ProductDetailScreen> createState() => _ProductDetailScreenState();
}

class _ProductDetailScreenState extends State<ProductDetailScreen> {
  int _currentImageIndex = 0;
  final PageController _pageController = PageController();
  UserModel? _sellerUser;
  String? _globalWhatsApp;
  String? _globalWeChat;

  @override
  void initState() {
    super.initState();
    _loadSellerUser();
    _loadGlobalContactInfo();
  }

  Future<void> _loadSellerUser() async {
    try {
      final user = await UserService.getUserById(widget.product.sellerId);
      if (mounted) {
        setState(() {
          _sellerUser = user;
        });
      }
    } catch (e) {
      // Handle error silently, user will just not see role indicator
    }
  }

  Future<void> _loadGlobalContactInfo() async {
    try {
      final whatsApp = await SettingsService.getGlobalWhatsApp();
      final weChat = await SettingsService.getGlobalWeChat();
      if (mounted) {
        setState(() {
          _globalWhatsApp = whatsApp;
          _globalWeChat = weChat;
        });
      }
    } catch (e) {
      // Handle error silently
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          // App Bar with Image Gallery
          SliverAppBar(
            expandedHeight: 400,
            pinned: true,
            backgroundColor: AppConstants.surfaceColor,
            leading: IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => Navigator.pop(context),
            ),
            actions: [
              IconButton(
                icon: const Icon(Icons.share),
                onPressed: _shareProduct,
              ),

            ],
            flexibleSpace: FlexibleSpaceBar(
              background: _buildImageGallery(),
            ),
          ),

          // Product Details
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Product Name and Price
                  _buildProductHeader(),

                  const SizedBox(height: AppConstants.paddingMedium),

                  // Description
                  _buildDescription(),

                  const SizedBox(height: AppConstants.paddingMedium),

                  // Live Link
                  if (widget.product.hasLiveLink) ...[
                    _buildLiveLink(),
                    const SizedBox(height: AppConstants.paddingMedium),
                  ],

                  // Specifications
                  if (widget.product.specifications.isNotEmpty)
                    _buildSpecifications(),

                  const SizedBox(height: AppConstants.paddingXLarge),

                  // Bottom padding for navigation gap
                  const SizedBox(height: 100),
                ],
              ),
            ),
          ),
        ],
      ),

      // Contact Buttons
      bottomNavigationBar: _buildContactButtons(),
    );
  }

  Widget _buildImageGallery() {
    if (widget.product.imageUrls.isEmpty) {
      return Container(
        color: AppConstants.backgroundColor,
        child: const Center(
          child: Icon(
            Icons.image_not_supported,
            size: 100,
            color: AppConstants.textHintColor,
          ),
        ),
      );
    }

    return Stack(
      children: [
        PageView.builder(
          controller: _pageController,
          onPageChanged: (index) {
            setState(() {
              _currentImageIndex = index;
            });
          },
          itemCount: widget.product.imageUrls.length,
          itemBuilder: (context, index) {
            return Container(
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: NetworkImage(widget.product.imageUrls[index]),
                  fit: BoxFit.cover,
                ),
              ),
            );
          },
        ),

        // Image Indicators
        if (widget.product.imageUrls.length > 1)
          Positioned(
            bottom: 16,
            left: 0,
            right: 0,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: widget.product.imageUrls.asMap().entries.map((entry) {
                return Container(
                  width: 8,
                  height: 8,
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: _currentImageIndex == entry.key
                        ? AppConstants.onPrimaryColor
                        : AppConstants.onPrimaryColor.withOpacity(0.4),
                  ),
                );
              }).toList(),
            ),
          ),
      ],
    );
  }

  Widget _buildProductHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.product.name,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),

        const SizedBox(height: AppConstants.paddingSmall),

        // Price section (only show if price > 0)
        if (widget.product.price > 0)
          Row(
            children: [
              Expanded(
                child: Row(
                  children: [
                    Text(
                      widget.product.formattedPrice,
                      style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                        color: AppConstants.primaryColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),

                    if (widget.product.isOnSale) ...[
                      const SizedBox(width: AppConstants.paddingSmall),
                      Text(
                        widget.product.formattedOriginalPrice,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: AppConstants.textSecondaryColor,
                          decoration: TextDecoration.lineThrough,
                        ),
                      ),
                      const SizedBox(width: AppConstants.paddingSmall),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppConstants.paddingSmall,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: AppConstants.errorColor,
                          borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                        ),
                        child: Text(
                          '${widget.product.discountPercentage.toStringAsFixed(0)}% OFF',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppConstants.onErrorColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),

            // Live Link Button (compact version)
            if (widget.product.hasLiveLink) ...[
              const SizedBox(width: AppConstants.paddingSmall),
              ElevatedButton.icon(
                onPressed: _openLiveLink,
                icon: const Icon(Icons.link, size: 16),
                label: const Text('Live Link', style: TextStyle(fontSize: 12)),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppConstants.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    vertical: 8,
                    horizontal: 12,
                  ),
                  minimumSize: const Size(0, 32),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                  ),
                ),
              ),
            ],
          ],
        ),

        const SizedBox(height: AppConstants.paddingSmall),

        Row(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.paddingSmall,
                vertical: 2,
              ),
              decoration: BoxDecoration(
                color: widget.product.isInStock
                    ? Colors.green.withOpacity(0.1)
                    : Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
              ),
              child: Text(
                widget.product.isInStock ? 'In Stock' : 'Out of Stock',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: widget.product.isInStock ? Colors.green : Colors.red,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),

            const SizedBox(width: AppConstants.paddingSmall),

            Text(
              'Category: ${widget.product.category}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppConstants.textSecondaryColor,
              ),
            ),
          ],
        ),
      ],
    );
  }





  Widget _buildDescription() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Description',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),

        const SizedBox(height: AppConstants.paddingSmall),

        Text(
          widget.product.description,
          style: Theme.of(context).textTheme.bodyMedium,
        ),
      ],
    );
  }

  Widget _buildLiveLink() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Live Link',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),

        const SizedBox(height: AppConstants.paddingSmall),

        Container(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _openLiveLink,
            icon: const Icon(Icons.link, color: Colors.white),
            label: const Text('Visit Live Link'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(
                vertical: AppConstants.paddingMedium,
                horizontal: AppConstants.paddingLarge,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSpecifications() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Specifications',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          decoration: BoxDecoration(
            color: AppConstants.surfaceColor,
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
            border: Border.all(color: AppConstants.backgroundColor),
          ),
          child: Column(
            children: widget.product.specifications.entries.map((entry) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      flex: 2,
                      child: Text(
                        entry.key,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    const Text(': '),
                    Expanded(
                      flex: 3,
                      child: Text(
                        entry.value.toString(),
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildContactButtons() {
    // Check if we have global contact info
    final hasGlobalWhatsApp = _globalWhatsApp != null && _globalWhatsApp!.isNotEmpty;
    final hasGlobalWeChat = _globalWeChat != null && _globalWeChat!.isNotEmpty;

    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: const BoxDecoration(
        color: AppConstants.surfaceColor,
        border: Border(
          top: BorderSide(color: AppConstants.backgroundColor),
        ),
      ),
      child: Row(
        children: [
          // WhatsApp Button (Global Contact)
          if (hasGlobalWhatsApp)
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _contactViaGlobalWhatsApp,
                icon: const Icon(Icons.chat, color: Colors.white),
                label: const Text('WhatsApp'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF25D366), // WhatsApp green
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: AppConstants.paddingMedium),
                ),
              ),
            ),

          if (hasGlobalWhatsApp && hasGlobalWeChat)
            const SizedBox(width: AppConstants.paddingMedium),

          // WeChat Button (Global Contact)
          if (hasGlobalWeChat)
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _contactViaGlobalWeChat,
                icon: const Icon(Icons.wechat, color: Colors.white),
                label: const Text('WeChat'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF1AAD19), // WeChat green
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: AppConstants.paddingMedium),
                ),
              ),
            ),

          // If no contact methods available
          if (!hasGlobalWhatsApp && !hasGlobalWeChat)
            Expanded(
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: AppConstants.paddingMedium),
                decoration: BoxDecoration(
                  color: AppConstants.backgroundColor,
                  borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                ),
                child: Text(
                  'No contact information available',
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppConstants.textSecondaryColor,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  void _contactViaGlobalWhatsApp() async {
    if (_globalWhatsApp == null || _globalWhatsApp!.isEmpty) return;

    try {
      final phone = _globalWhatsApp!.replaceAll(RegExp(r'[^\d+]'), '');
      final url = Uri.parse('https://wa.me/$phone?text=Hi, I\'m interested in your product: ${widget.product.name}');

      if (await canLaunchUrl(url)) {
        await launchUrl(url, mode: LaunchMode.externalApplication);
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Could not open WhatsApp'),
              backgroundColor: AppConstants.errorColor,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Invalid WhatsApp number'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    }
  }

  void _contactViaGlobalWeChat() {
    if (_globalWeChat == null || _globalWeChat!.isEmpty) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('WeChat Contact'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('WeChat ID:'),
            const SizedBox(height: AppConstants.paddingSmall),
            SelectableText(
              _globalWeChat!,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppConstants.primaryColor,
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            const Text('Copy the WeChat ID and search for it in your WeChat app.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _openLiveLink() async {
    if (!widget.product.hasLiveLink) return;

    try {
      final url = Uri.parse(widget.product.liveLink!);

      if (await canLaunchUrl(url)) {
        await launchUrl(url, mode: LaunchMode.externalApplication);
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Could not open the live link'),
              backgroundColor: AppConstants.errorColor,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Invalid live link format'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    }
  }

  void _shareProduct() async {
    try {
      final String shareText = '''
🛍️ Check out this amazing product!

${widget.product.name}
${widget.product.price > 0 ? '💰 Price: ${widget.product.formattedPrice}' : ''}
📦 Category: ${widget.product.category}

${widget.product.description}

👤 Seller: ${widget.product.sellerName}
${widget.product.sellerLocation != null ? '📍 Location: ${widget.product.sellerLocation}' : ''}

Download Amal Point app to see more products!
''';

      await Share.share(
        shareText,
        subject: 'Check out ${widget.product.name} on Amal Point',
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to share product'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    }
  }
}
